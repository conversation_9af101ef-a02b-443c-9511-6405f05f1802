<?php
/**
 * Test page for Premium Download System
 * Add this to your WordPress root and access via: yoursite.com/test-premium-downloads.php
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Check if user is admin
if (!current_user_can('administrator')) {
    die('Access denied. Admin only.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Premium Download System Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-box { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007cba; }
        .premium { border-left-color: #FFD700; background: #fffbf0; }
        .regular { border-left-color: #28a745; background: #f0fff4; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .success { border-left-color: #28a745; background: #d4edda; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        th { background: #f1f1f1; }
        .premium-btn { background: linear-gradient(135deg, #FFD700, #FFA500); color: #000; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-weight: bold; }
        .regular-btn { background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-weight: bold; }
    </style>
</head>
<body>
    <h1>Premium Download System Test</h1>
    
    <?php
    // Test current user's membership status
    $current_user_id = get_current_user_id();
    $is_premium = dooplay_check_premium_membership();
    
    echo '<div class="test-box ' . ($is_premium ? 'premium' : 'regular') . '">';
    echo '<h2>Current User Status</h2>';
    if ($current_user_id) {
        echo '<p><strong>User ID:</strong> ' . $current_user_id . '</p>';
        echo '<p><strong>Username:</strong> ' . wp_get_current_user()->user_login . '</p>';
        echo '<p><strong>Premium Status:</strong> ' . ($is_premium ? '👑 Premium Member' : '👤 Regular User') . '</p>';
        
        // Show ARMember details if available
        if (function_exists('arm_get_user_membership_detail')) {
            $user_membership = arm_get_user_membership_detail($current_user_id);
            if (!empty($user_membership)) {
                echo '<p><strong>ARMember Plans:</strong></p><ul>';
                foreach ($user_membership as $membership) {
                    $status = isset($membership['arm_user_plan_status']) && $membership['arm_user_plan_status'] == 1 ? 'Active' : 'Inactive';
                    echo '<li>Plan ID: ' . $membership['arm_plan_id'] . ' - Status: ' . $status . '</li>';
                }
                echo '</ul>';
            }
        }
    } else {
        echo '<p>No user logged in</p>';
    }
    echo '</div>';
    
    // Test movies with download links
    $movies = get_posts(array(
        'post_type' => 'movies',
        'posts_per_page' => 5,
        'post_status' => 'publish'
    ));
    
    if (!empty($movies)) {
        echo '<div class="test-box success">';
        echo '<h2>Download Link Test</h2>';
        echo '<table>';
        echo '<tr><th>Movie</th><th>Has Links</th><th>Download Button Type</th><th>Action</th></tr>';
        
        foreach ($movies as $movie) {
            $has_links = doo_here_links($movie->ID);
            $has_download = doo_here_type_links($movie->ID, __d('Download'));
            
            echo '<tr>';
            echo '<td><a href="' . get_permalink($movie->ID) . '" target="_blank">' . $movie->post_title . '</a></td>';
            echo '<td>' . ($has_links ? '✅ Yes' : '❌ No') . '</td>';
            
            if ($has_download) {
                if ($is_premium) {
                    echo '<td>👑 Premium (Direct Download)</td>';
                    echo '<td><a href="' . get_permalink($movie->ID) . '" class="premium-btn" target="_blank">Test Premium Download</a></td>';
                } else {
                    echo '<td>👤 Regular (Short Link)</td>';
                    echo '<td><a href="' . get_permalink($movie->ID) . '" class="regular-btn" target="_blank">Test Regular Download</a></td>';
                }
            } else {
                echo '<td>❌ No Download Links</td>';
                echo '<td>-</td>';
            }
            echo '</tr>';
        }
        
        echo '</table>';
        echo '</div>';
    }
    
    // Test ARMember integration
    echo '<div class="test-box">';
    echo '<h2>ARMember Integration Test</h2>';
    
    if (function_exists('arm_get_user_membership_detail')) {
        echo '<p>✅ ARMember plugin is active</p>';
        
        // Get premium plan IDs from settings
        $premium_plan_ids = get_option('dooplay_premium_plan_ids', '2,3,4');
        echo '<p><strong>Configured Premium Plan IDs:</strong> ' . $premium_plan_ids . '</p>';
        
        // Test membership check function
        if (function_exists('arm_is_member_active')) {
            echo '<p>✅ arm_is_member_active function available</p>';
        } else {
            echo '<p>❌ arm_is_member_active function not available</p>';
        }
        
    } else {
        echo '<p>❌ ARMember plugin not active or not found</p>';
    }
    echo '</div>';
    
    // Configuration instructions
    echo '<div class="test-box">';
    echo '<h2>Configuration Instructions</h2>';
    echo '<ol>';
    echo '<li>Go to <strong>Settings > Premium Downloads</strong> in WordPress admin</li>';
    echo '<li>Configure your ARMember premium plan IDs</li>';
    echo '<li>Set your membership page URL</li>';
    echo '<li>Test with different user accounts (premium vs regular)</li>';
    echo '</ol>';
    echo '</div>';
    ?>
    
    <div class="test-box">
        <h2>How the System Works</h2>
        <ul>
            <li><strong>Premium Users:</strong> See golden download buttons that link directly to files</li>
            <li><strong>Regular Users:</strong> See green download buttons that go through short link pages</li>
            <li><strong>Premium Badge:</strong> Shows on quality column for premium users</li>
            <li><strong>Notifications:</strong> Different messages for premium vs regular users</li>
            <li><strong>Tracking:</strong> Premium downloads are tracked for analytics</li>
        </ul>
    </div>
</body>
</html>
