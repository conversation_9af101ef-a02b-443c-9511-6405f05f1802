<?php
/**
 * Test page to debug links issue
 * Add this to your WordPress root and access via: yoursite.com/test-links-debug.php
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Check if user is admin
if (!current_user_can('administrator')) {
    die('Access denied. Admin only.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Links Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-box { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007cba; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .success { border-left-color: #28a745; background: #d4edda; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        th { background: #f1f1f1; }
    </style>
</head>
<body>
    <h1>Links Debug Test</h1>
    
    <?php
    // Get some movies to test
    $movies = get_posts(array(
        'post_type' => 'movies',
        'posts_per_page' => 10,
        'post_status' => 'publish'
    ));
    
    if (empty($movies)) {
        echo '<div class="debug-box error">No movies found in the database.</div>';
    } else {
        echo '<div class="debug-box success">Found ' . count($movies) . ' movies to test.</div>';
        
        echo '<table>';
        echo '<tr><th>Movie Title</th><th>Post ID</th><th>Has Links</th><th>Download Links</th><th>Torrent Links</th><th>Watch Links</th><th>Buy Links</th></tr>';
        
        foreach ($movies as $movie) {
            $post_id = $movie->ID;
            $title = $movie->post_title;
            
            // Check links using the theme functions
            $has_links = doo_here_links($post_id);
            $has_download = doo_here_type_links($post_id, __d('Download'));
            $has_torrent = doo_here_type_links($post_id, __d('Torrent'));
            $has_watch = doo_here_type_links($post_id, __d('Watch online'));
            $has_buy = doo_here_type_links($post_id, __d('Rent or Buy'));
            
            echo '<tr>';
            echo '<td><a href="' . get_permalink($post_id) . '" target="_blank">' . $title . '</a></td>';
            echo '<td>' . $post_id . '</td>';
            echo '<td>' . ($has_links ? '✅ Yes' : '❌ No') . '</td>';
            echo '<td>' . ($has_download ? '✅ Yes' : '❌ No') . '</td>';
            echo '<td>' . ($has_torrent ? '✅ Yes' : '❌ No') . '</td>';
            echo '<td>' . ($has_watch ? '✅ Yes' : '❌ No') . '</td>';
            echo '<td>' . ($has_buy ? '✅ Yes' : '❌ No') . '</td>';
            echo '</tr>';
        }
        
        echo '</table>';
    }
    
    // Check dt_links post type
    echo '<h2>Direct Database Check</h2>';
    
    global $wpdb;
    $links_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'dt_links' AND post_status = 'publish'");
    
    echo '<div class="debug-box">';
    echo '<strong>Total dt_links in database:</strong> ' . $links_count;
    echo '</div>';
    
    if ($links_count > 0) {
        $sample_links = $wpdb->get_results("SELECT * FROM {$wpdb->posts} WHERE post_type = 'dt_links' AND post_status = 'publish' LIMIT 5");
        
        echo '<h3>Sample Links:</h3>';
        echo '<table>';
        echo '<tr><th>Link ID</th><th>Parent Post</th><th>Type</th><th>URL</th><th>Quality</th></tr>';
        
        foreach ($sample_links as $link) {
            $parent_title = get_the_title($link->post_parent);
            $type = get_post_meta($link->ID, '_dool_type', true);
            $url = get_post_meta($link->ID, '_dool_url', true);
            $quality = get_post_meta($link->ID, '_dool_quality', true);
            
            echo '<tr>';
            echo '<td>' . $link->ID . '</td>';
            echo '<td>' . $parent_title . ' (ID: ' . $link->post_parent . ')</td>';
            echo '<td>' . $type . '</td>';
            echo '<td>' . substr($url, 0, 50) . '...</td>';
            echo '<td>' . $quality . '</td>';
            echo '</tr>';
        }
        
        echo '</table>';
    }
    ?>
    
    <div class="debug-box">
        <h3>How to use this debug info:</h3>
        <ul>
            <li>If "Has Links" shows ❌ No for all movies, then no links are being created properly</li>
            <li>If specific link types show ❌ No, check the link type values in the database</li>
            <li>Check if the movie detail page URL shows links by visiting the movie page</li>
            <li>Add <code>?debug_links=1</code> to any movie page URL to see debug comments in HTML source</li>
        </ul>
    </div>
</body>
</html>
